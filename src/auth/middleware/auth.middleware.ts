import {
  Injectable,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { PrismaService } from '../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import * as jwt from 'jsonwebtoken';
import { UserInfo } from '../interfaces/user-info.interface';

export interface AuthenticatedRequest extends Request {
  user?: UserInfo;
}

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new UnauthorizedException('No valid token provided');
      }

      const token = authHeader.substring(7);

      // Verify token with JWT
      const decoded = jwt.verify(
        token,
        this.configService.get<string>('JWT_SECRET') || 'your-secret-key',
      ) as any;

      // Validate user exists and is active
      const user = await this.prisma.user.findUnique({
        where: { id: decoded.sub },
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      if (user.status !== 'ACTIVE') {
        throw new UnauthorizedException('User account is not active');
      }

      // Check token version for global revocation
      if (decoded.tokenVersion !== user.tokenVersion) {
        throw new UnauthorizedException('Token has been revoked');
      }

      // Attach user info to request object
      req.user = {
        id: user.id,
        username: user.username,
        email: user.email,
        type: user.type,
        businessType: user.businessType,
        applicantId: user.applicantId || undefined,
        status: user.status,
      };

      next();
    } catch (error) {
      if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('Invalid token');
      }
      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('Token expired');
      }
      throw error;
    }
  }
}
