import { KycStatus } from '../dto/create-kyc.dto';
import { StatusTransitionMap, KycBusinessRules } from '../interfaces/kyc.interface';

/**
 * Business rules and configuration constants for KYC
 */
export const KYC_BUSINESS_RULES: KycBusinessRules = {
  minAge: 17,
  maxAge: 120,
  supportedCountries: ['IDN', 'INDONESIA', 'ID'],
  supportedProviders: ['MANUAL', 'SUMSUB'],
  allowedFileExtensions: ['.jpg', '.jpeg', '.png', '.pdf'],
  maxFileNameLength: 255,
  minAddressLength: 10,
  maxAddressLength: 500,
};

/**
 * Valid user statuses for KYC operations
 */
export const VALID_USER_STATUSES = ['ACTIVE', 'VERIFIED'];

/**
 * Status transitions allowed for regular users
 */
export const USER_EDITABLE_STATUSES = [
  KycStatus.PENDING,
  KycStatus.REJECTED,
  KycStatus.DENIED,
];

/**
 * Status that are locked and cannot be modified by users
 */
export const LOCKED_STATUSES = [
  KycStatus.APPROVED,
  KycStatus.MANUAL_REVIEW,
  KycStatus.LOCKED,
  KycStatus.CANCELED,
];

/**
 * Valid status transitions map
 */
export const VALID_STATUS_TRANSITIONS: StatusTransitionMap = {
  [KycStatus.PENDING]: [
    KycStatus.APPROVED,
    KycStatus.REJECTED,
    KycStatus.MANUAL_REVIEW,
    KycStatus.CANCELED,
  ],
  [KycStatus.MANUAL_REVIEW]: [
    KycStatus.APPROVED,
    KycStatus.REJECTED,
    KycStatus.DENIED,
    KycStatus.PENDING,
  ],
  [KycStatus.REJECTED]: [
    KycStatus.PENDING,
    KycStatus.DENIED,
    KycStatus.APPROVED,
  ],
  [KycStatus.DENIED]: [
    KycStatus.PENDING,
  ],
  [KycStatus.APPROVED]: [
    KycStatus.LOCKED,
    KycStatus.MANUAL_REVIEW,
  ],
  [KycStatus.CANCELED]: [
    KycStatus.PENDING,
  ],
  [KycStatus.LOCKED]: [
    KycStatus.APPROVED,
    KycStatus.MANUAL_REVIEW,
  ],
};

/**
 * Status transitions that require admin privileges
 */
export const ADMIN_ONLY_TRANSITIONS = [
  { from: KycStatus.DENIED, to: KycStatus.PENDING },
  { from: KycStatus.APPROVED, to: KycStatus.LOCKED },
  { from: KycStatus.CANCELED, to: KycStatus.PENDING },
  { from: KycStatus.LOCKED, to: KycStatus.APPROVED },
];

/**
 * Status messages for different KYC statuses
 */
export const STATUS_MESSAGES = {
  [KycStatus.PENDING]: 'KYC is under verification process',
  [KycStatus.APPROVED]: 'KYC has been approved',
  [KycStatus.REJECTED]: 'KYC was rejected, please correct the data and resubmit',
  [KycStatus.DENIED]: 'KYC was permanently denied',
  [KycStatus.CANCELED]: 'KYC was canceled',
  [KycStatus.MANUAL_REVIEW]: 'KYC requires manual review',
  [KycStatus.LOCKED]: 'KYC is locked',
};

/**
 * Sumsub status mapping to KYC status
 */
export const SUMSUB_STATUS_MAPPING = {
  completed: KycStatus.APPROVED,
  approved: KycStatus.APPROVED,
  rejected: KycStatus.REJECTED,
  pending: KycStatus.PENDING,
  init: KycStatus.PENDING,
  queued: KycStatus.PENDING,
  onHold: KycStatus.MANUAL_REVIEW,
  final_rejected: KycStatus.DENIED,
};

/**
 * Regular expressions for validation
 */
export const VALIDATION_PATTERNS = {
  indonesianPhone: /^\+62[0-9]{8,13}$/,
  indonesianZip: /^[0-9]{5}$/,
  ktpNumber: /^\d{16}$/,
  passportNumber: /^[A-Z]\d{7}$/,
  driverLicense: /^[A-Z0-9]{10,15}$/,
  fileName: /^[a-zA-Z0-9._-]+\.(jpg|jpeg|png|pdf)$/i,
  applicantId: /^[a-zA-Z0-9_-]+$/,
};

/**
 * Invalid patterns to check against
 */
export const INVALID_PATTERNS = {
  repeatedChars: /^(.)\1{9,}$/,
  testData: /test|dummy|fake|sample/i,
  allZerosPhone: /^\+620{8,}$/,
  allOnesPhone: /^\+621{8,}$/,
  executableFiles: /\.(exe|bat|cmd|scr|vbs)$/i,
  tempFiles: /^temp/i,
};

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
  USER_ID_REQUIRED: 'User ID is required',
  USER_ID_INVALID: 'User ID must be a string',
  USER_ID_EMPTY: 'User ID cannot be empty',
  USER_NOT_FOUND: 'User not found',
  USER_NOT_ACTIVE: 'User account is not active',
  KYC_EXISTS: 'User already has KYC data',
  KYC_NOT_FOUND: 'KYC data not found',
  INVALID_BIRTHDATE: 'Invalid birthdate format',
  BIRTHDATE_FUTURE: 'Birthdate cannot be in the future',
  AGE_TOO_YOUNG: 'Minimum age requirement not met',
  AGE_UNREALISTIC: 'Age seems unrealistic, please verify birthdate',
  PHONE_INVALID_COUNTRY: 'Phone number must use Indonesian country code (+62)',
  PHONE_INVALID_FORMAT: 'Invalid Indonesian phone number format',
  APPLICANT_ID_INVALID: 'Invalid applicant ID format',
  APPLICANT_ID_TOO_SHORT: 'Applicant ID must be at least 3 characters',
  APPLICANT_ID_TOO_LONG: 'Applicant ID must not exceed 100 characters',
  APPLICANT_ID_DUPLICATE: 'Applicant ID already exists for another user',
  PHONE_INVALID_PATTERN: 'Phone number contains invalid pattern',
  ADDRESS_TOO_SHORT: 'Address must be at least 10 characters long',
  ADDRESS_TOO_LONG: 'Address must not exceed 500 characters',
  ADDRESS_SUSPICIOUS: 'Address appears to contain invalid or test data',
  COUNTRY_UNSUPPORTED: 'Currently only Indonesian residents are supported',
  ZIP_INVALID: 'Indonesian postal code must be exactly 5 digits',
  ZIP_INVALID_CODE: 'Invalid postal code',
  FILE_INVALID_EXTENSION: 'File must have a valid extension',
  FILE_NAME_TOO_LONG: 'File name must not exceed 255 characters',
  FILE_SUSPICIOUS: 'File name appears to be invalid or suspicious',
  PROVIDER_UNSUPPORTED: 'Unsupported KYC provider',
  STATUS_TRANSITION_INVALID: 'Invalid status transition',
  STATUS_TRANSITION_FORBIDDEN: 'Status transition requires admin privileges',
  UPDATE_FORBIDDEN: 'KYC data cannot be modified in current status',
};
