# Initial Tech Start
using `yarn` for package manager in this project.

# Products Schema Database
model Product {
     id
     displayName 
     tokenName
     underlyingName
     symbol
     img
     description
     price
     high
     low
     priceChange24h
     priceChangePct24h
     apy
     tvl     
     createdAt  
     updatedAt   
}

model supported_network {
     id
     network_name
     network_code
     chain_id
     address
     decimals
     productId
     createdAt  
     updatedAt
}

for the id using uuid v7 same as file schema.prisma

# flow
after adding at file schema.prisma, check package.json for prisma version. Don't forget using yarn for package manager.