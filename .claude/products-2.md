# enhance middleware authorization
check `/Users/<USER>/work/projects/backend/orbitum/src/auth` I want you to enhance flow authorization. Make middleware and include information id, username, email, type, businessType, applicantId. 

# implementation
check the flow auth and make middlware using information above. Using clean code pattern and best practice for that. So the middleware can implement at another route if protect authorization.