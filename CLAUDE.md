# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a NestJS-based backend API for Orbitum, a financial services platform providing user management, authentication, and KYC/KYB (Know Your Customer/Know Your Business) services. The application uses PostgreSQL with Prisma ORM and includes Safe Global integration for multi-signature wallet operations.

## Development Commands

### Essential Commands
- `yarn start` - Start the application (kills port 3001 first)
- `yarn start:dev` - Start in development mode with watch
- `yarn start:debug` - Start with debugging enabled
- `yarn build` - Build the application
- `yarn lint` - Run ESLint with auto-fix
- `yarn format` - Format code with Prettier

### Testing
- `yarn test` - Run unit tests
- `yarn test:watch` - Run tests in watch mode
- `yarn test:cov` - Run tests with coverage
- `yarn test:e2e` - Run end-to-end tests

### Database Operations
- `yarn prisma:generate` - Generate Prisma client
- `yarn prisma:migrate` - Run database migrations
- `yarn prisma:studio` - Open Prisma Studio

### Port Management
- `yarn kill-port` - Kill process on port 3001
- `yarn kill-all-node` - Kill all ts-node processes
- `yarn kill-nest` - Kill all nest processes

### Deployment
- `yarn deploy` - Deploy to production (Fly.io)
- `yarn deploy:staging` - Deploy to staging (Fly.io)

## Architecture

### Core Modules
- **Auth Module** (`src/auth/`) - JWT authentication, Google OAuth, local auth
- **Users Module** (`src/users/`) - User management with consumer/business types
- **KYC Module** (`src/kyc/`) - Know Your Customer verification workflows
- **Email Module** (`src/email/`) - SendGrid email integration
- **Prisma Module** (`src/prisma/`) - Database service layer
- **Common Module** (`src/common/`) - Shared pipes, filters, decorators

### Key Features
- JWT-based authentication with refresh tokens
- Google OAuth integration
- Multi-signature wallet operations via Safe Global
- Comprehensive KYC/KYB verification system
- Role-based user management (Consumer/Business)
- Email notifications
- API documentation with Swagger

### Database Schema
The application uses Prisma with PostgreSQL, featuring:
- Users with types (CONSUMER/BUSINESS)
- KYC verification records with status tracking
- Business entities with various business types
- Authentication tokens and sessions
- Safe transaction records

### API Structure
- Base URL: `/api/v1`
- Swagger docs: `/api/docs`
- Health check: `/api/v1/health`
- API info: `/api/v1/info`

## Development Notes

### Environment Variables
- Use `.env-example` as template
- Required: `DATABASE_URL`, `JWT_SECRET`, `GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET`
- Optional: `SENDGRID_API_KEY`, `FRONTEND_URL`

### Port Configuration
- Default port: 3000 (development), 3001 (production)
- Scripts automatically handle port conflicts

### Error Handling
- Global validation pipes
- Custom HTTP exception filters
- Graceful shutdown with OAuth error recovery
- Comprehensive logging with different environments

### Security
- JWT token authentication
- CORS configuration for production/development
- Input validation with class-validator
- Global exception handling

### Testing
- Jest test framework
- Unit tests in `*.spec.ts` files
- E2E tests in `/test` directory
- Test coverage reporting available

## Special Considerations

### Safe Global Integration
- Uses Safe Global SDK for multi-signature operations
- Handles safe transaction creation and execution
- Includes proper error handling for blockchain operations

### KYC/KYB System
- Comprehensive verification workflow
- Multiple status states (PENDING, APPROVED, REJECTED, etc.)
- Integration with external verification services
- Business-specific verification requirements

### Authentication Flow
- Multiple auth strategies (local, Google OAuth)
- JWT with refresh token rotation
- Device tracking and session management
- Secure password hashing with bcrypt